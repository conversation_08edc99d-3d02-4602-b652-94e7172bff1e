import { useState, useEffect } from 'react';
import <PERSON><PERSON><PERSON><PERSON> from 'jszip';
import { saveAs } from 'file-saver';

import Header from './components/Header';
import Footer from './components/Footer';
import Controls from './components/Controls';
import ImageGrid from './components/ImageGrid';
import { useTheme } from './hooks/useTheme';
import { ImageService } from './services/imageService';

function App() {
  const { isDark, toggleTheme } = useTheme();
  const [url, setUrl] = useState('');
  // Start with demo images immediately to prevent blank screen
  const [images, setImages] = useState(() => {
    try {
      return ImageService.getDemoImages();
    } catch (error) {
      console.error('Error getting demo images:', error);
      return [];
    }
  });
  const [isLoading, setIsLoading] = useState(false);
  const [loadingMessage, setLoadingMessage] = useState('');
  const [progress, setProgress] = useState(0);
  const [viewMode, setViewMode] = useState('grid');

  const updateProgress = (value, message) => {
    setProgress(value);
    setLoadingMessage(message);
  };

  const fetchImages = async (mode = 'url') => {
    if (mode === 'demo') {
      setIsLoading(true);
      setProgress(0);
      updateProgress(20, 'Loading demo images...');

      try {
        await new Promise(resolve => setTimeout(resolve, 300));
        updateProgress(60, 'Preparing images...');
        await new Promise(resolve => setTimeout(resolve, 300));

        const demoImages = ImageService.getDemoImages();
        updateProgress(100, 'Demo images loaded!');
        setImages(demoImages);
      } catch (error) {
        console.error('Error loading demo images:', error);
        updateProgress(100, 'Error loading demo images');
      } finally {
        setIsLoading(false);
        setTimeout(() => {
          setLoadingMessage('');
          setProgress(0);
        }, 1000);
      }
      return;
    }

    if (!url.trim()) return;

    setIsLoading(true);
    setProgress(0);
    updateProgress(10, 'Fetching webpage...');

    try {
      // Simulate progress during fetch
      await new Promise(resolve => setTimeout(resolve, 500));
      updateProgress(30, 'Extracting images...');

      const fetchedImages = await ImageService.fetchImagesFromUrl(url);
      updateProgress(70, 'Validating images...');

      // Simulate validation progress
      if (fetchedImages.length > 0) {
        for (let i = 0; i < fetchedImages.length; i++) {
          const progressValue = 70 + (20 * (i + 1) / fetchedImages.length);
          updateProgress(progressValue, `Validating image ${i + 1} of ${fetchedImages.length}...`);
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      }

      updateProgress(100, 'Complete!');
      setImages(fetchedImages);
    } catch (error) {
      console.error('Error fetching images:', error);
      updateProgress(100, 'Error occurred, showing demo images');
      const demoImages = ImageService.getDemoImages();
      setImages(demoImages);
    } finally {
      setIsLoading(false);
      setTimeout(() => {
        setLoadingMessage('');
        setProgress(0);
      }, 1500);
    }
  };

  const toggleImageSelection = (imageId) => {
    setImages(prevImages =>
      prevImages.map(img =>
        img.id === imageId ? { ...img, selected: !img.selected } : img
      )
    );
  };

  const selectAllImages = () => {
    setImages(prevImages =>
      prevImages.map(img => ({ ...img, selected: true }))
    );
  };

  const deselectAllImages = () => {
    setImages(prevImages =>
      prevImages.map(img => ({ ...img, selected: false }))
    );
  };

  const downloadSelectedAsZip = async () => {
    const selectedImages = images.filter(img => img.selected);
    if (selectedImages.length === 0) return;

    setIsLoading(true);
    setProgress(0);
    updateProgress(5, 'Preparing download...');

    const zip = new JSZip();
    const promises = [];

    selectedImages.forEach((image, index) => {
      const promise = fetch(image.url)
        .then(response => response.blob())
        .then(blob => {
          const filename = `image-${index + 1}.jpg`;
          zip.file(filename, blob);
          const progressValue = 10 + (70 * (index + 1) / selectedImages.length);
          updateProgress(progressValue, `Downloaded ${index + 1} of ${selectedImages.length} images...`);
        })
        .catch(error => {
          console.error(`Failed to download image ${image.url}:`, error);
        });

      promises.push(promise);
    });

    try {
      await Promise.all(promises);
      updateProgress(85, 'Creating ZIP file...');
      const content = await zip.generateAsync({ type: 'blob' });
      updateProgress(100, 'Download complete!');
      saveAs(content, 'selected-images.zip');
    } catch (error) {
      console.error('Error creating ZIP file:', error);
      updateProgress(100, 'Error creating ZIP file');
    } finally {
      setIsLoading(false);
      setTimeout(() => {
        setLoadingMessage('');
        setProgress(0);
      }, 1500);
    }
  };

  const downloadAllAsZip = async () => {
    if (images.length === 0) return;

    setIsLoading(true);
    setProgress(0);
    updateProgress(5, 'Preparing download...');

    const zip = new JSZip();
    const promises = [];

    images.forEach((image, index) => {
      const promise = fetch(image.url)
        .then(response => response.blob())
        .then(blob => {
          const filename = `image-${index + 1}.jpg`;
          zip.file(filename, blob);
          const progressValue = 10 + (70 * (index + 1) / images.length);
          updateProgress(progressValue, `Downloaded ${index + 1} of ${images.length} images...`);
        })
        .catch(error => {
          console.error(`Failed to download image ${image.url}:`, error);
        });

      promises.push(promise);
    });

    try {
      await Promise.all(promises);
      updateProgress(85, 'Creating ZIP file...');
      const content = await zip.generateAsync({ type: 'blob' });
      updateProgress(100, 'Download complete!');
      saveAs(content, 'all-images.zip');
    } catch (error) {
      console.error('Error creating ZIP file:', error);
      updateProgress(100, 'Error creating ZIP file');
    } finally {
      setIsLoading(false);
      setTimeout(() => {
        setLoadingMessage('');
        setProgress(0);
      }, 1500);
    }
  };

  const selectedCount = images.filter(img => img.selected).length;

  const appStyle = {
    minHeight: '100vh',
    display: 'flex',
    flexDirection: 'column',
    backgroundColor: isDark ? '#111827' : '#f9fafb',
    opacity: 1,
    visibility: 'visible'
  };

  const mainStyle = {
    flex: 1,
    maxWidth: '1280px',
    margin: '0 auto',
    padding: '2rem 1rem',
    width: '100%'
  };

  const contentStyle = {
    display: 'flex',
    flexDirection: 'column',
    gap: '2rem'
  };

  // Ensure we always have content to show
  const safeImages = images && images.length > 0 ? images : [
    {
      id: 'fallback-1',
      url: 'https://picsum.photos/400/300?random=1',
      alt: 'Demo Image 1',
      selected: false
    },
    {
      id: 'fallback-2',
      url: 'https://picsum.photos/400/300?random=2',
      alt: 'Demo Image 2',
      selected: false
    },
    {
      id: 'fallback-3',
      url: 'https://picsum.photos/400/300?random=3',
      alt: 'Demo Image 3',
      selected: false
    }
  ];

  return (
    <div style={appStyle}>
      <Header isDark={isDark} toggleTheme={toggleTheme} />

      <main style={mainStyle}>
        <div style={contentStyle}>
          <Controls
            url={url}
            setUrl={setUrl}
            onFetchImages={fetchImages}
            isLoading={isLoading}
            loadingMessage={loadingMessage}
            progress={progress}
            viewMode={viewMode}
            setViewMode={setViewMode}
            images={safeImages}
            onSelectAll={selectAllImages}
            onDeselectAll={deselectAllImages}
            onDownloadSelected={downloadSelectedAsZip}
            onDownloadAll={downloadAllAsZip}
            selectedCount={selectedCount}
            isDark={isDark}
          />

          <ImageGrid
            images={safeImages}
            onToggleSelect={toggleImageSelection}
            viewMode={viewMode}
            isDark={isDark}
            isLoading={false}
          />
        </div>
      </main>

      <Footer isDark={isDark} />
    </div>
  );
}

export default App;
