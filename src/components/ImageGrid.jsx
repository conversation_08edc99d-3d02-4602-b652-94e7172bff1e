import SimpleImageCard from './SimpleImageCard';
import { Image as ImageIcon } from 'lucide-react';

const ImageGrid = ({ images, onToggleSelect, viewMode, isDark, isLoading }) => {
  // Ensure we always have images to show
  const safeImages = images && images.length > 0 ? images : [];

  // Show loading state only if no images and loading
  if (isLoading && safeImages.length === 0) {
    const loadingStyle = {
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      padding: '4rem 1rem',
      color: isDark ? '#9ca3af' : '#6b7280'
    };

    const spinnerStyle = {
      width: '2rem',
      height: '2rem',
      border: '3px solid transparent',
      borderTop: '3px solid #3b82f6',
      borderRadius: '50%',
      animation: 'spin 1s linear infinite',
      marginRight: '1rem'
    };

    return (
      <div style={loadingStyle}>
        <div style={spinnerStyle}></div>
        <span>Loading images...</span>
      </div>
    );
  }

  if (safeImages.length === 0) {
    const emptyStateStyle = {
      textAlign: 'center',
      padding: '3rem 1rem',
      color: isDark ? '#9ca3af' : '#6b7280'
    };

    const titleStyle = {
      fontSize: '1.125rem',
      fontWeight: '500',
      color: isDark ? '#f9fafb' : '#111827',
      margin: '1rem 0 0.5rem 0'
    };

    const descriptionStyle = {
      maxWidth: '28rem',
      margin: '0 auto',
      lineHeight: '1.5'
    };

    return (
      <div style={emptyStateStyle}>
        <ImageIcon size={64} color={isDark ? '#4b5563' : '#d1d5db'} style={{ margin: '0 auto 1rem' }} />
        <h3 style={titleStyle}>
          No images found
        </h3>
        <p style={descriptionStyle}>
          Enter a URL above to fetch images from a webpage. The app will extract all images
          found on the page including img tags, background images, and picture elements.
        </p>
        <div style={{
          marginTop: '1rem',
          padding: '1rem',
          backgroundColor: isDark ? '#1f2937' : '#f0f9ff',
          borderRadius: '0.5rem',
          border: `1px solid ${isDark ? '#374151' : '#bae6fd'}`,
          fontSize: '0.875rem',
          color: isDark ? '#d1d5db' : '#0c4a6e'
        }}>
          <strong>💡 Tips for best results:</strong>
          <ul style={{ margin: '0.5rem 0', paddingLeft: '1.5rem' }}>
            <li>Try: <code>http://localhost:5173/test-page.html</code> (local test page)</li>
            <li>Many external sites block cross-origin requests (CORS)</li>
            <li>The app uses multiple CORS proxy services to bypass restrictions</li>
            <li>Falls back to demo images if real fetching fails</li>
            <li>Extracts images from img tags, CSS backgrounds, and picture elements</li>
          </ul>
        </div>
      </div>
    );
  }

  if (viewMode === 'list') {
    const listStyle = {
      display: 'flex',
      flexDirection: 'column',
      gap: '0.75rem'
    };

    return (
      <div style={listStyle}>
        {safeImages.map((image) => (
          <SimpleImageCard
            key={image.id}
            image={image}
            onToggleSelect={onToggleSelect}
            viewMode={viewMode}
            isDark={isDark}
          />
        ))}
      </div>
    );
  }

  // Grid view
  const gridStyle = {
    display: 'grid',
    gridTemplateColumns: 'repeat(auto-fill, minmax(250px, 1fr))',
    gap: '1rem'
  };

  return (
    <div style={gridStyle}>
      {safeImages.map((image) => (
        <SimpleImageCard
          key={image.id}
          image={image}
          onToggleSelect={onToggleSelect}
          viewMode={viewMode}
          isDark={isDark}
        />
      ))}
    </div>
  );
};

export default ImageGrid;
