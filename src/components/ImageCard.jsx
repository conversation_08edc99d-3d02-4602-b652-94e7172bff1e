import React, { useState, useEffect } from 'react';
import { Download, Copy, Check, ExternalLink, Image as ImageIcon, CheckCircle, Circle } from 'lucide-react';
import { ImageService } from '../services/imageService';

const ImageCard = ({ image, onToggleSelect, viewMode, isDark }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [imageError, setImageError] = useState(false);
  const [copied, setCopied] = useState(false);
  const [downloaded, setDownloaded] = useState(false);
  const [imageInfo, setImageInfo] = useState({ size: null, format: null });

  // Get image info safely
  useEffect(() => {
    if (!imageError && image.url && !imageInfo.size) {
      getImageInfo(image.url);
    }
  }, [image.url, imageError, imageInfo.size]);

  const getImageInfo = async (url) => {
    try {
      const img = new Image();
      img.crossOrigin = 'anonymous';

      img.onload = () => {
        const format = getImageFormat(url);
        setImageInfo({
          size: `${img.naturalWidth} × ${img.naturalHeight}`,
          format: format
        });
      };

      img.src = url;
    } catch (error) {
      console.error('Failed to get image info:', error);
    }
  };

  const getImageFormat = (url) => {
    const extension = url.match(/\.(jpg|jpeg|png|gif|webp|svg|bmp)(\?.*)?$/i);
    if (extension) {
      return extension[1].toUpperCase();
    }
    return 'Unknown';
  };

  const showAlert = (message, type = 'success') => {
    // Create alert element
    const alert = document.createElement('div');
    alert.textContent = message;
    alert.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: ${type === 'success' ? '#10b981' : '#ef4444'};
      color: white;
      padding: 12px 20px;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      z-index: 10000;
      font-family: system-ui, sans-serif;
      font-size: 14px;
      font-weight: 500;
      transform: translateX(100%);
      transition: transform 0.3s ease;
    `;

    document.body.appendChild(alert);

    // Animate in
    setTimeout(() => {
      alert.style.transform = 'translateX(0)';
    }, 10);

    // Remove after 3 seconds
    setTimeout(() => {
      alert.style.transform = 'translateX(100%)';
      setTimeout(() => {
        if (document.body.contains(alert)) {
          document.body.removeChild(alert);
        }
      }, 300);
    }, 3000);
  };

  const handleDownload = async () => {
    setIsLoading(true);
    try {
      const filename = `image-${image.id}.${getImageFormat(image.url).toLowerCase()}`;
      await ImageService.downloadImage(image.url, filename);
      setDownloaded(true);
      showAlert('Image downloaded successfully!');
      setTimeout(() => setDownloaded(false), 2000);
    } catch (error) {
      console.error('Download failed:', error);
      showAlert('Download failed. Opening in new tab...', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCopyUrl = async () => {
    try {
      await navigator.clipboard.writeText(image.url);
      setCopied(true);
      showAlert('URL copied to clipboard!');
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('Failed to copy URL:', error);
      showAlert('Failed to copy URL', 'error');
    }
  };

  const handleImageError = () => {
    setImageError(true);
  };

  const handleCardClick = (e) => {
    // Only toggle selection if clicking on the card itself, not on buttons
    if (e.target === e.currentTarget || e.target.tagName === 'IMG') {
      onToggleSelect(image.id);
    }
  };

  if (viewMode === 'list') {
    const listCardStyle = {
      backgroundColor: isDark ? '#1f2937' : 'white',
      borderRadius: '0.5rem',
      boxShadow: image.selected ? '0 4px 12px rgba(59, 130, 246, 0.3)' : '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
      border: image.selected ? '2px solid #3b82f6' : `1px solid ${isDark ? '#374151' : '#e5e7eb'}`,
      padding: '1rem',
      display: 'flex',
      alignItems: 'center',
      gap: '1rem',
      cursor: 'pointer',
      position: 'relative'
    };

    const thumbnailStyle = {
      flexShrink: 0,
      width: '4rem',
      height: '4rem',
      backgroundColor: isDark ? '#374151' : '#f3f4f6',
      borderRadius: '0.5rem',
      overflow: 'hidden',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      position: 'relative'
    };

    const selectionIconStyle = {
      position: 'absolute',
      top: '0.25rem',
      right: '0.25rem',
      zIndex: 10,
      backgroundColor: 'rgba(0, 0, 0, 0.7)',
      borderRadius: '50%',
      padding: '0.125rem'
    };

    const imageStyle = {
      width: '100%',
      height: '100%',
      objectFit: 'cover'
    };

    const infoStyle = {
      flex: 1,
      minWidth: 0
    };

    const titleStyle = {
      fontSize: '0.875rem',
      fontWeight: '500',
      color: isDark ? '#f9fafb' : '#111827',
      margin: '0 0 0.25rem 0',
      overflow: 'hidden',
      textOverflow: 'ellipsis',
      whiteSpace: 'nowrap'
    };

    const urlStyle = {
      fontSize: '0.875rem',
      color: isDark ? '#9ca3af' : '#6b7280',
      margin: '0 0 0.25rem 0',
      overflow: 'hidden',
      textOverflow: 'ellipsis',
      whiteSpace: 'nowrap'
    };

    const dimensionsStyle = {
      fontSize: '0.75rem',
      color: isDark ? '#6b7280' : '#9ca3af',
      margin: 0
    };

    const imageInfoStyle = {
      fontSize: '0.75rem',
      color: isDark ? '#9ca3af' : '#6b7280',
      margin: '0.25rem 0 0 0',
      display: 'flex',
      gap: '0.5rem'
    };

    const formatBadgeStyle = {
      backgroundColor: isDark ? '#374151' : '#f3f4f6',
      color: isDark ? '#d1d5db' : '#374151',
      padding: '0.125rem 0.375rem',
      borderRadius: '0.25rem',
      fontSize: '0.625rem',
      fontWeight: '600'
    };

    const actionsStyle = {
      display: 'flex',
      alignItems: 'center',
      gap: '0.5rem'
    };

    const actionButtonStyle = {
      padding: '0.5rem',
      backgroundColor: 'transparent',
      border: 'none',
      borderRadius: '0.25rem',
      cursor: 'pointer',
      color: isDark ? '#9ca3af' : '#6b7280',
      transition: 'color 0.2s'
    };

    return (
      <div style={listCardStyle} onClick={handleCardClick}>
        {/* Selection icon overlay */}
        <div style={selectionIconStyle}>
          {image.selected ? (
            <CheckCircle size={20} color="#3b82f6" />
          ) : (
            <Circle size={20} color={isDark ? '#9ca3af' : '#6b7280'} />
          )}
        </div>

        {/* Image thumbnail */}
        <div style={thumbnailStyle}>
          {imageError ? (
            <ImageIcon size={24} color={isDark ? '#6b7280' : '#9ca3af'} />
          ) : (
            <img
              src={image.url}
              alt={image.alt}
              style={imageStyle}
              onError={handleImageError}
            />
          )}
        </div>

        {/* Image info */}
        <div style={infoStyle}>
          <p style={titleStyle}>
            {image.alt}
          </p>
          <p style={urlStyle}>
            {image.url}
          </p>
          <div style={imageInfoStyle}>
            {imageInfo.size && (
              <span>{imageInfo.size}</span>
            )}
            {imageInfo.format && (
              <span style={formatBadgeStyle}>{imageInfo.format}</span>
            )}
          </div>
        </div>

        {/* Actions */}
        <div style={actionsStyle}>
          <button
            onClick={(e) => { e.stopPropagation(); handleCopyUrl(); }}
            style={actionButtonStyle}
            title="Copy URL"
          >
            {copied ? (
              <Check size={16} color="#10b981" />
            ) : (
              <Copy size={16} />
            )}
          </button>

          <a
            href={image.url}
            target="_blank"
            rel="noopener noreferrer"
            style={actionButtonStyle}
            title="Open in new tab"
            onClick={(e) => e.stopPropagation()}
          >
            <ExternalLink size={16} />
          </a>

          <button
            onClick={(e) => { e.stopPropagation(); handleDownload(); }}
            disabled={isLoading}
            className="btn-primary"
            style={{
              fontSize: '0.75rem',
              padding: '0.25rem 0.75rem',
              backgroundColor: downloaded ? '#10b981' : undefined
            }}
            title="Download image"
          >
            {isLoading ? (
              <div style={{
                width: '1rem',
                height: '1rem',
                border: '2px solid transparent',
                borderTop: '2px solid white',
                borderRadius: '50%',
                animation: 'spin 1s linear infinite'
              }}></div>
            ) : downloaded ? (
              <Check size={16} />
            ) : (
              <Download size={16} />
            )}
          </button>
        </div>
      </div>
    );
  }

  // Grid view
  const gridCardStyle = {
    backgroundColor: isDark ? '#1f2937' : 'white',
    borderRadius: '0.5rem',
    boxShadow: image.selected ? '0 8px 25px rgba(59, 130, 246, 0.3)' : '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
    border: image.selected ? '2px solid #3b82f6' : `1px solid ${isDark ? '#374151' : '#e5e7eb'}`,
    overflow: 'hidden',
    cursor: 'pointer'
  };

  const imageContainerStyle = {
    position: 'relative',
    aspectRatio: '1',
    backgroundColor: isDark ? '#374151' : '#f3f4f6'
  };

  const checkboxContainerStyle = {
    position: 'absolute',
    top: '0.5rem',
    left: '0.5rem',
    zIndex: 10,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    borderRadius: '50%',
    padding: '0.25rem'
  };

  const imageInfoOverlayStyle = {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    background: 'linear-gradient(transparent, rgba(0, 0, 0, 0.8))',
    color: 'white',
    padding: '1rem 0.75rem 0.75rem',
    fontSize: '0.75rem',
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'end'
  };

  const gridImageStyle = {
    width: '100%',
    height: '100%',
    objectFit: 'cover'
  };

  const overlayStyle = {
    position: 'absolute',
    inset: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    opacity: 0,
    transition: 'opacity 0.2s'
  };

  const overlayButtonsStyle = {
    display: 'flex',
    gap: '0.5rem'
  };

  const overlayButtonStyle = {
    padding: '0.5rem',
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: '50%',
    border: 'none',
    cursor: 'pointer',
    color: '#374151',
    transition: 'background-color 0.2s'
  };

  const infoContainerStyle = {
    padding: '0.75rem'
  };

  const gridTitleStyle = {
    fontSize: '0.875rem',
    fontWeight: '500',
    color: isDark ? '#f9fafb' : '#111827',
    margin: '0 0 0.25rem 0',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    whiteSpace: 'nowrap'
  };

  const gridDimensionsStyle = {
    fontSize: '0.75rem',
    color: isDark ? '#9ca3af' : '#6b7280',
    margin: 0
  };

  return (
    <div
      style={gridCardStyle}
      onClick={handleCardClick}
      onMouseEnter={(e) => {
        const overlay = e.currentTarget.querySelector('.overlay');
        if (overlay) overlay.style.opacity = '1';
      }}
      onMouseLeave={(e) => {
        const overlay = e.currentTarget.querySelector('.overlay');
        if (overlay) overlay.style.opacity = '0';
      }}
    >
      {/* Image */}
      <div style={imageContainerStyle}>
        {/* Selection icon */}
        <div style={checkboxContainerStyle}>
          {image.selected ? (
            <CheckCircle size={20} color="#3b82f6" />
          ) : (
            <Circle size={20} color="white" />
          )}
        </div>

        {/* Image */}
        {imageError ? (
          <div style={{
            width: '100%',
            height: '100%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}>
            <ImageIcon size={48} color={isDark ? '#6b7280' : '#9ca3af'} />
          </div>
        ) : (
          <>
            <img
              src={image.url}
              alt={image.alt}
              style={gridImageStyle}
              onError={handleImageError}
            />
            {/* Image info overlay */}
            <div style={imageInfoOverlayStyle}>
              <div>
                {imageInfo.size && <div>{imageInfo.size}</div>}
              </div>
              {imageInfo.format && (
                <div style={{
                  backgroundColor: 'rgba(255, 255, 255, 0.2)',
                  padding: '0.125rem 0.375rem',
                  borderRadius: '0.25rem',
                  fontSize: '0.625rem',
                  fontWeight: '600'
                }}>
                  {imageInfo.format}
                </div>
              )}
            </div>
          </>
        )}

        {/* Overlay with actions */}
        <div className="overlay" style={overlayStyle}>
          <div style={overlayButtonsStyle}>
            <button
              onClick={(e) => { e.stopPropagation(); handleCopyUrl(); }}
              style={overlayButtonStyle}
              title="Copy URL"
            >
              {copied ? (
                <Check size={16} color="#10b981" />
              ) : (
                <Copy size={16} />
              )}
            </button>

            <a
              href={image.url}
              target="_blank"
              rel="noopener noreferrer"
              style={overlayButtonStyle}
              title="Open in new tab"
              onClick={(e) => e.stopPropagation()}
            >
              <ExternalLink size={16} />
            </a>

            <button
              onClick={(e) => { e.stopPropagation(); handleDownload(); }}
              disabled={isLoading}
              style={{
                ...overlayButtonStyle,
                backgroundColor: downloaded ? 'rgba(16, 185, 129, 0.9)' : 'rgba(255, 255, 255, 0.9)'
              }}
              title="Download image"
            >
              {isLoading ? (
                <div style={{
                  width: '1rem',
                  height: '1rem',
                  border: '2px solid transparent',
                  borderTop: '2px solid #374151',
                  borderRadius: '50%',
                  animation: 'spin 1s linear infinite'
                }}></div>
              ) : downloaded ? (
                <Check size={16} color="white" />
              ) : (
                <Download size={16} />
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Image info */}
      <div style={infoContainerStyle}>
        <p style={gridTitleStyle}>
          {image.alt}
        </p>
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginTop: '0.25rem'
        }}>
          {imageInfo.size && (
            <span style={gridDimensionsStyle}>{imageInfo.size}</span>
          )}
          {imageInfo.format && (
            <span style={{
              ...formatBadgeStyle,
              fontSize: '0.625rem'
            }}>
              {imageInfo.format}
            </span>
          )}
        </div>
      </div>
    </div>
  );
};

export default ImageCard;
