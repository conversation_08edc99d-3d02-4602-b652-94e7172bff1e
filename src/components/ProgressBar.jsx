import React from 'react';

const ProgressBar = ({ progress, message, isDark }) => {
  const containerStyle = {
    width: '100%',
    backgroundColor: isDark ? '#374151' : '#e5e7eb',
    borderRadius: '0.5rem',
    overflow: 'hidden',
    height: '0.5rem',
    marginBottom: '0.5rem'
  };

  const fillStyle = {
    height: '100%',
    background: 'linear-gradient(90deg, #3b82f6, #1d4ed8)',
    borderRadius: '0.5rem',
    transition: 'width 0.3s ease',
    width: `${Math.min(100, Math.max(0, progress))}%`,
    position: 'relative',
    overflow: 'hidden'
  };

  const shimmerStyle = {
    position: 'absolute',
    top: 0,
    left: '-100%',
    width: '100%',
    height: '100%',
    background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent)',
    animation: progress > 0 && progress < 100 ? 'shimmer 1.5s infinite' : 'none'
  };

  const messageStyle = {
    fontSize: '0.875rem',
    color: isDark ? '#d1d5db' : '#374151',
    textAlign: 'center',
    marginBottom: '0.5rem'
  };

  return (
    <div>
      {message && (
        <div style={messageStyle}>
          {message}
        </div>
      )}
      <div style={containerStyle}>
        <div style={fillStyle}>
          <div style={shimmerStyle}></div>
        </div>
      </div>
      <div style={{
        fontSize: '0.75rem',
        color: isDark ? '#9ca3af' : '#6b7280',
        textAlign: 'center',
        marginTop: '0.25rem'
      }}>
        {Math.round(progress)}%
      </div>
    </div>
  );
};

export default ProgressBar;
