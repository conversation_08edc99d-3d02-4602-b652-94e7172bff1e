const Footer = ({ isDark }) => {
  const footerStyle = {
    backgroundColor: isDark ? '#1f2937' : 'white',
    borderTop: `1px solid ${isDark ? '#374151' : '#e5e7eb'}`,
    marginTop: 'auto'
  };

  const containerStyle = {
    maxWidth: '1280px',
    margin: '0 auto',
    padding: '1.5rem 1rem'
  };

  const contentStyle = {
    display: 'flex',
    flexDirection: 'column',
    gap: '1rem',
    alignItems: 'center'
  };

  const textStyle = {
    fontSize: '0.875rem',
    color: isDark ? '#9ca3af' : '#6b7280'
  };

  const featuresStyle = {
    display: 'flex',
    flexWrap: 'wrap',
    alignItems: 'center',
    gap: '1.5rem',
    fontSize: '0.875rem',
    color: isDark ? '#9ca3af' : '#6b7280'
  };

  const featureStyle = {
    display: 'flex',
    alignItems: 'center',
    gap: '0.25rem'
  };

  const dotStyle = {
    width: '8px',
    height: '8px',
    borderRadius: '50%'
  };

  return (
    <footer style={footerStyle}>
      <div style={containerStyle}>
        <div style={contentStyle}>
          <div style={textStyle}>
            © 2024 Image Downloader. Built with React & Modern CSS.
          </div>

          <div style={featuresStyle}>
            <span>Features:</span>
            <span style={featureStyle}>
              <span style={{...dotStyle, backgroundColor: '#10b981'}}></span>
              <span>Bulk Download</span>
            </span>
            <span style={featureStyle}>
              <span style={{...dotStyle, backgroundColor: '#3b82f6'}}></span>
              <span>ZIP Export</span>
            </span>
            <span style={featureStyle}>
              <span style={{...dotStyle, backgroundColor: '#8b5cf6'}}></span>
              <span>Dark Mode</span>
            </span>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
