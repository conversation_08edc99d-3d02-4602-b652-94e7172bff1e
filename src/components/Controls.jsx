import {
  Search,
  Grid3X3,
  List,
  Download,
  Archive,
  CheckSquare,
  Square,
  Copy,
  Loader2
} from 'lucide-react';
import ProgressBar from './ProgressBar';

const Controls = ({
  url,
  setUrl,
  onFetchImages,
  isLoading,
  loadingMessage,
  progress,
  viewMode,
  setViewMode,
  images,
  onSelectAll,
  onDeselectAll,
  onDownloadSelected,
  onDownloadAll,
  selectedCount,
  isDark
}) => {
  const handleSubmit = (e) => {
    e.preventDefault();
    if (url.trim()) {
      onFetchImages();
    }
  };

  const handleCopyUrls = async () => {
    const selectedImages = images.filter(img => img.selected);
    const urls = selectedImages.map(img => img.url).join('\n');
    
    try {
      await navigator.clipboard.writeText(urls);
      // You could add a toast notification here
      console.log('URLs copied to clipboard');
    } catch (error) {
      console.error('Failed to copy URLs:', error);
    }
  };

  const allSelected = images.length > 0 && images.every(img => img.selected);
  const hasImages = images.length > 0;
  const hasSelected = selectedCount > 0;

  const containerStyle = {
    display: 'flex',
    flexDirection: 'column',
    gap: '1.5rem'
  };

  const formStyle = {
    display: 'flex',
    gap: '0.75rem',
    alignItems: 'stretch'
  };

  const inputContainerStyle = {
    flex: 1
  };

  const buttonStyle = {
    minWidth: '120px'
  };

  return (
    <div style={containerStyle}>
      {/* URL Input */}
      <form onSubmit={handleSubmit} style={formStyle}>
        <div style={inputContainerStyle}>
          <input
            type="url"
            value={url}
            onChange={(e) => setUrl(e.target.value)}
            placeholder="Enter URL to fetch images from... (try: http://localhost:5173/test-page.html)"
            className="input-field"
            required
          />
        </div>
        <button
          type="submit"
          disabled={isLoading || !url.trim()}
          className="btn-primary"
          style={buttonStyle}
        >
          {isLoading ? (
            <>
              <Loader2 size={16} style={{ animation: 'spin 1s linear infinite' }} />
              <span>{loadingMessage || 'Fetching...'}</span>
            </>
          ) : (
            <>
              <Search size={16} />
              <span>Fetch Images</span>
            </>
          )}
        </button>

        <button
          type="button"
          onClick={() => onFetchImages('demo')}
          disabled={isLoading}
          className="btn-secondary"
          style={{ minWidth: '100px' }}
        >
          <span>Load Demo</span>
        </button>
      </form>

      {/* Progress Bar */}
      {isLoading && (
        <div style={{
          padding: '1rem',
          backgroundColor: isDark ? '#1f2937' : '#f9fafb',
          borderRadius: '0.5rem',
          border: `1px solid ${isDark ? '#374151' : '#e5e7eb'}`
        }}>
          <ProgressBar
            progress={progress || 0}
            message={loadingMessage}
            isDark={isDark}
          />
        </div>
      )}

      {/* Controls Bar */}
      {hasImages && (
        <div style={{
          display: 'flex',
          flexDirection: 'column',
          gap: '1rem',
          padding: '1rem',
          backgroundColor: isDark ? '#1f2937' : '#f9fafb',
          borderRadius: '0.5rem',
          border: `1px solid ${isDark ? '#374151' : '#e5e7eb'}`
        }}>
          {/* Top row - View controls and selection */}
          <div style={{
            display: 'flex',
            flexWrap: 'wrap',
            alignItems: 'center',
            gap: '1rem'
          }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
              <span style={{
                fontSize: '0.875rem',
                fontWeight: '500',
                color: isDark ? '#d1d5db' : '#374151'
              }}>
                View:
              </span>
              <div style={{
                display: 'flex',
                backgroundColor: isDark ? '#374151' : 'white',
                borderRadius: '0.5rem',
                padding: '0.25rem',
                border: `1px solid ${isDark ? '#4b5563' : '#d1d5db'}`
              }}>
                <button
                  onClick={() => setViewMode('grid')}
                  style={{
                    padding: '0.5rem',
                    borderRadius: '0.25rem',
                    border: 'none',
                    backgroundColor: viewMode === 'grid' ? '#3b82f6' : 'transparent',
                    color: viewMode === 'grid' ? 'white' : (isDark ? '#9ca3af' : '#6b7280'),
                    cursor: 'pointer',
                    display: 'flex',
                    alignItems: 'center'
                  }}
                >
                  <Grid3X3 size={16} />
                </button>
                <button
                  onClick={() => setViewMode('list')}
                  style={{
                    padding: '0.5rem',
                    borderRadius: '0.25rem',
                    border: 'none',
                    backgroundColor: viewMode === 'list' ? '#3b82f6' : 'transparent',
                    color: viewMode === 'list' ? 'white' : (isDark ? '#9ca3af' : '#6b7280'),
                    cursor: 'pointer',
                    display: 'flex',
                    alignItems: 'center'
                  }}
                >
                  <List size={16} />
                </button>
              </div>
            </div>

            <button
              onClick={allSelected ? onDeselectAll : onSelectAll}
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem',
                fontSize: '0.875rem',
                color: '#3b82f6',
                background: 'none',
                border: 'none',
                cursor: 'pointer'
              }}
            >
              {allSelected ? (
                <CheckSquare size={16} />
              ) : (
                <Square size={16} />
              )}
              <span>{allSelected ? 'Deselect All' : 'Select All'}</span>
            </button>

            <div style={{
              fontSize: '0.875rem',
              color: isDark ? '#9ca3af' : '#6b7280'
            }}>
              {selectedCount} of {images.length} selected
            </div>
          </div>

          {/* Bottom row - Action buttons */}
          <div style={{
            display: 'flex',
            flexWrap: 'wrap',
            gap: '0.75rem'
          }}>
            {hasSelected && (
              <button
                onClick={handleCopyUrls}
                className="btn-secondary"
              >
                <Copy size={16} />
                <span>Copy URLs</span>
              </button>
            )}

            <button
              onClick={onDownloadSelected}
              disabled={!hasSelected}
              className="btn-primary"
            >
              <Archive size={16} />
              <span>Download Selected ({selectedCount})</span>
            </button>

            <button
              onClick={onDownloadAll}
              className="btn-secondary"
            >
              <Download size={16} />
              <span>Download All</span>
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default Controls;
