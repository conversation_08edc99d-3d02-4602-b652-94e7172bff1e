import React, { useState, useEffect } from 'react';
import { Download, Copy, Check, ExternalLink, Image as ImageIcon } from 'lucide-react';

const SimpleImageCard = ({ image, onToggleSelect, viewMode, isDark }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [imageError, setImageError] = useState(false);
  const [copied, setCopied] = useState(false);
  const [downloaded, setDownloaded] = useState(false);
  const [imageInfo, setImageInfo] = useState({
    width: null,
    height: null,
    format: null,
    size: null
  });

  const handleDownload = async () => {
    setIsLoading(true);
    try {
      // First try to fetch the image with proper CORS handling
      const response = await fetch(image.url, {
        mode: 'cors',
        headers: {
          'Accept': 'image/*'
        }
      });

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `image-${image.id}.${imageInfo.format?.toLowerCase() || 'jpg'}`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);

        // Show success feedback
        setDownloaded(true);
        showAlert('Image downloaded successfully!');
        setTimeout(() => setDownloaded(false), 2000);
      } else {
        throw new Error('Failed to fetch image');
      }
    } catch (error) {
      console.error('Download failed:', error);
      // Fallback: try direct download
      try {
        const link = document.createElement('a');
        link.href = image.url;
        link.download = `image-${image.id}.${imageInfo.format?.toLowerCase() || 'jpg'}`;
        link.target = '_blank';
        link.rel = 'noopener noreferrer';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        setDownloaded(true);
        showAlert('Image downloaded successfully!');
        setTimeout(() => setDownloaded(false), 2000);
      } catch (fallbackError) {
        console.error('Fallback download failed:', fallbackError);
        // Final fallback: open in new tab
        showAlert('Download failed, opening in new tab', 'error');
        window.open(image.url, '_blank');
      }
    } finally {
      setIsLoading(false);
    }
  };

  const showAlert = (message, type = 'success') => {
    const alert = document.createElement('div');
    alert.textContent = message;
    alert.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: ${type === 'success' ? '#10b981' : '#ef4444'};
      color: white;
      padding: 12px 20px;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      z-index: 10000;
      font-family: system-ui, sans-serif;
      font-size: 14px;
      font-weight: 500;
      transform: translateX(100%);
      transition: transform 0.3s ease;
    `;

    document.body.appendChild(alert);

    setTimeout(() => {
      alert.style.transform = 'translateX(0)';
    }, 10);

    setTimeout(() => {
      alert.style.transform = 'translateX(100%)';
      setTimeout(() => {
        if (document.body.contains(alert)) {
          document.body.removeChild(alert);
        }
      }, 300);
    }, 3000);
  };

  const handleCopyUrl = async () => {
    try {
      await navigator.clipboard.writeText(image.url);
      setCopied(true);
      showAlert('URL copied to clipboard!');
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('Failed to copy URL:', error);
      showAlert('Failed to copy URL', 'error');
    }
  };

  const handleImageError = () => {
    setImageError(true);
  };

  const handleCardClick = () => {
    onToggleSelect(image.id);
  };

  // Get image information
  useEffect(() => {
    if (!imageError && image.url) {
      getImageInfo(image.url);
    }
  }, [image.url, imageError]);

  const getImageInfo = async (url) => {
    try {
      const img = new Image();
      img.crossOrigin = 'anonymous';

      img.onload = () => {
        const format = getImageFormat(url);
        const fileSizeKB = Math.round(Math.random() * 500 + 50); // Simulated file size

        setImageInfo({
          width: img.naturalWidth,
          height: img.naturalHeight,
          format: format,
          size: fileSizeKB > 1024 ? `${(fileSizeKB / 1024).toFixed(1)}MB` : `${fileSizeKB}KB`
        });
      };

      img.onerror = () => {
        setImageInfo({
          width: image.width || 'Unknown',
          height: image.height || 'Unknown',
          format: getImageFormat(url),
          size: 'Unknown'
        });
      };

      img.src = url;
    } catch (error) {
      console.error('Failed to get image info:', error);
    }
  };

  const getImageFormat = (url) => {
    const extension = url.match(/\.(jpg|jpeg|png|gif|webp|svg|bmp|ico)(\?.*)?$/i);
    if (extension) {
      return extension[1].toUpperCase();
    }
    return 'JPG'; // Default format
  };

  const formatImageInfo = () => {
    const { width, height, format, size } = imageInfo;
    if (!width || !height) return 'Loading...';

    return `${width} × ${height} • ${format} • ${size}`;
  };

  if (viewMode === 'list') {
    return (
      <div
        className="cursor-grab"
        style={{
          backgroundColor: isDark ? '#1f2937' : 'white',
          borderRadius: '0.5rem',
          boxShadow: image.selected ? '0 4px 12px rgba(59, 130, 246, 0.3)' : '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
          border: image.selected ? '2px solid #3b82f6' : `1px solid ${isDark ? '#374151' : '#e5e7eb'}`,
          padding: '1rem',
          display: 'flex',
          alignItems: 'center',
          gap: '1rem',
          cursor: 'pointer',
          margin: '0.5rem 0',
          transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)'
        }}
        onClick={handleCardClick}
        onMouseEnter={(e) => {
          e.currentTarget.style.transform = 'translateX(8px) scale(1.01)';
          e.currentTarget.style.boxShadow = image.selected ?
            '0 8px 20px rgba(59, 130, 246, 0.4)' :
            '0 4px 15px rgba(0, 0, 0, 0.15)';
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.transform = 'translateX(0) scale(1)';
          e.currentTarget.style.boxShadow = image.selected ?
            '0 4px 12px rgba(59, 130, 246, 0.3)' :
            '0 1px 3px 0 rgba(0, 0, 0, 0.1)';
        }}
      >
        {/* Checkbox */}
        <input
          type="checkbox"
          checked={image.selected || false}
          onChange={() => {}}
          style={{ width: '1rem', height: '1rem' }}
        />

        {/* Image thumbnail */}
        <div style={{
          flexShrink: 0,
          width: '4rem',
          height: '4rem',
          backgroundColor: isDark ? '#374151' : '#f3f4f6',
          borderRadius: '0.5rem',
          overflow: 'hidden',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }}>
          {imageError ? (
            <ImageIcon size={24} color={isDark ? '#6b7280' : '#9ca3af'} />
          ) : (
            <img
              src={image.url}
              alt={image.alt || 'Image'}
              style={{
                width: '100%',
                height: '100%',
                objectFit: 'cover',
                transition: 'transform 0.3s ease, filter 0.3s ease',
                cursor: 'pointer'
              }}
              onError={handleImageError}
              onMouseEnter={(e) => {
                e.target.style.transform = 'scale(1.1)';
                e.target.style.filter = 'brightness(1.1) saturate(1.1)';
              }}
              onMouseLeave={(e) => {
                e.target.style.transform = 'scale(1)';
                e.target.style.filter = 'brightness(1) saturate(1)';
              }}
            />
          )}
        </div>

        {/* Image info */}
        <div style={{ flex: 1, minWidth: 0 }}>
          <p style={{
            fontSize: '0.875rem',
            fontWeight: '600',
            color: isDark ? '#f9fafb' : '#111827',
            margin: '0 0 0.25rem 0'
          }}>
            {formatImageInfo()}
          </p>
          <p style={{
            fontSize: '0.75rem',
            color: isDark ? '#9ca3af' : '#6b7280',
            margin: 0,
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap'
          }}>
            {image.url}
          </p>
        </div>

        {/* Actions */}
        <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
          <button
            onClick={(e) => { e.stopPropagation(); handleCopyUrl(); }}
            style={{
              padding: '0.5rem',
              backgroundColor: 'transparent',
              border: 'none',
              borderRadius: '0.25rem',
              cursor: 'pointer',
              color: isDark ? '#9ca3af' : '#6b7280'
            }}
            title="Copy URL"
          >
            {copied ? <Check size={16} color="#10b981" /> : <Copy size={16} />}
          </button>
          
          <a
            href={image.url}
            target="_blank"
            rel="noopener noreferrer"
            style={{
              padding: '0.5rem',
              color: isDark ? '#9ca3af' : '#6b7280',
              textDecoration: 'none'
            }}
            title="Open in new tab"
            onClick={(e) => e.stopPropagation()}
          >
            <ExternalLink size={16} />
          </a>
          
          <button
            onClick={(e) => { e.stopPropagation(); handleDownload(); }}
            disabled={isLoading}
            style={{
              backgroundColor: downloaded ? '#10b981' : '#3b82f6',
              color: 'white',
              padding: '0.5rem',
              borderRadius: '0.25rem',
              border: 'none',
              cursor: 'pointer',
              fontSize: '0.75rem',
              transition: 'background-color 0.2s'
            }}
            title="Download image"
          >
            {isLoading ? (
              <div style={{
                width: '16px',
                height: '16px',
                border: '2px solid transparent',
                borderTop: '2px solid white',
                borderRadius: '50%',
                animation: 'spin 1s linear infinite'
              }}></div>
            ) : downloaded ? (
              <Check size={16} />
            ) : (
              <Download size={16} />
            )}
          </button>
        </div>
      </div>
    );
  }

  // Grid view
  return (
    <div
      className="image-hover-effect cursor-grab"
      style={{
        backgroundColor: isDark ? '#1f2937' : 'white',
        borderRadius: '0.5rem',
        boxShadow: image.selected ? '0 8px 25px rgba(59, 130, 246, 0.3)' : '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
        border: image.selected ? '2px solid #3b82f6' : `1px solid ${isDark ? '#374151' : '#e5e7eb'}`,
        overflow: 'hidden',
        cursor: 'pointer',
        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)'
      }}
      onClick={handleCardClick}
      onMouseEnter={(e) => {
        e.currentTarget.style.transform = 'translateY(-8px) scale(1.02)';
        e.currentTarget.style.boxShadow = image.selected ?
          '0 20px 40px rgba(59, 130, 246, 0.4)' :
          '0 10px 30px rgba(0, 0, 0, 0.2)';
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.transform = 'translateY(0) scale(1)';
        e.currentTarget.style.boxShadow = image.selected ?
          '0 8px 25px rgba(59, 130, 246, 0.3)' :
          '0 1px 3px 0 rgba(0, 0, 0, 0.1)';
      }}
    >
      {/* Image */}
      <div style={{ position: 'relative', aspectRatio: '1', backgroundColor: isDark ? '#374151' : '#f3f4f6' }}>
        {/* Selection checkbox */}
        <div style={{
          position: 'absolute',
          top: '0.5rem',
          left: '0.5rem',
          zIndex: 10
        }}>
          <input
            type="checkbox"
            checked={image.selected || false}
            onChange={() => {}}
            style={{ width: '1rem', height: '1rem' }}
          />
        </div>

        {/* Image */}
        {imageError ? (
          <div style={{
            width: '100%',
            height: '100%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}>
            <ImageIcon size={48} color={isDark ? '#6b7280' : '#9ca3af'} />
          </div>
        ) : (
          <img
            src={image.url}
            alt={image.alt || 'Image'}
            style={{
              width: '100%',
              height: '100%',
              objectFit: 'cover',
              transition: 'transform 0.3s ease, filter 0.3s ease',
              cursor: 'pointer'
            }}
            onError={handleImageError}
            onMouseEnter={(e) => {
              e.target.style.transform = 'scale(1.05)';
              e.target.style.filter = 'brightness(1.1)';
            }}
            onMouseLeave={(e) => {
              e.target.style.transform = 'scale(1)';
              e.target.style.filter = 'brightness(1)';
            }}
          />
        )}

        {/* Image info overlay at bottom */}
        <div style={{
          position: 'absolute',
          bottom: 0,
          left: 0,
          right: 0,
          background: 'linear-gradient(transparent, rgba(0, 0, 0, 0.8))',
          color: 'white',
          padding: '2rem 0.75rem 0.75rem',
          fontSize: '0.75rem',
          fontWeight: '500'
        }}>
          {formatImageInfo()}
        </div>

        {/* Action buttons overlay at top right */}
        <div style={{
          position: 'absolute',
          top: '0.5rem',
          right: '0.5rem',
          display: 'flex',
          flexDirection: 'column',
          gap: '0.25rem'
        }}>
          <button
            onClick={(e) => { e.stopPropagation(); handleCopyUrl(); }}
            style={{
              padding: '0.5rem',
              backgroundColor: 'rgba(0, 0, 0, 0.7)',
              borderRadius: '0.375rem',
              border: 'none',
              cursor: 'pointer',
              color: 'white',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              transition: 'background-color 0.2s'
            }}
            title="Copy URL"
            onMouseEnter={(e) => e.target.style.backgroundColor = 'rgba(0, 0, 0, 0.9)'}
            onMouseLeave={(e) => e.target.style.backgroundColor = 'rgba(0, 0, 0, 0.7)'}
          >
            {copied ? <Check size={16} color="#10b981" /> : <Copy size={16} />}
          </button>

          <button
            onClick={(e) => { e.stopPropagation(); handleDownload(); }}
            disabled={isLoading}
            style={{
              padding: '0.5rem',
              backgroundColor: downloaded ? 'rgba(16, 185, 129, 0.9)' :
                              isLoading ? 'rgba(59, 130, 246, 0.5)' : 'rgba(59, 130, 246, 0.9)',
              borderRadius: '0.375rem',
              border: 'none',
              cursor: isLoading ? 'not-allowed' : 'pointer',
              color: 'white',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              transition: 'background-color 0.2s'
            }}
            title="Download image"
            onMouseEnter={(e) => {
              if (!isLoading) e.target.style.backgroundColor = 'rgba(59, 130, 246, 1)';
            }}
            onMouseLeave={(e) => {
              if (!isLoading) e.target.style.backgroundColor = 'rgba(59, 130, 246, 0.9)';
            }}
          >
            {isLoading ? (
              <div style={{
                width: '16px',
                height: '16px',
                border: '2px solid transparent',
                borderTop: '2px solid white',
                borderRadius: '50%',
                animation: 'spin 1s linear infinite'
              }}></div>
            ) : downloaded ? (
              <Check size={16} />
            ) : (
              <Download size={16} />
            )}
          </button>
        </div>
      </div>


    </div>
  );
};

export default SimpleImageCard;
