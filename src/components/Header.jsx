import { Moon, Sun, Download } from 'lucide-react';

const Header = ({ isDark, toggleTheme }) => {
  const headerStyle = {
    backgroundColor: isDark ? '#1f2937' : 'white',
    borderBottom: `1px solid ${isDark ? '#374151' : '#e5e7eb'}`,
    boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
    color: isDark ? '#f9fafb' : '#111827'
  };

  const containerStyle = {
    maxWidth: '1280px',
    margin: '0 auto',
    padding: '0 1rem'
  };

  const flexStyle = {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    height: '4rem'
  };

  const logoContainerStyle = {
    display: 'flex',
    alignItems: 'center',
    gap: '0.75rem'
  };

  const logoStyle = {
    backgroundColor: '#3b82f6',
    padding: '0.5rem',
    borderRadius: '0.5rem',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center'
  };

  const titleStyle = {
    fontSize: '1.25rem',
    fontWeight: 'bold',
    margin: 0,
    color: isDark ? '#f9fafb' : '#111827'
  };

  const subtitleStyle = {
    fontSize: '0.875rem',
    color: isDark ? '#9ca3af' : '#6b7280',
    margin: 0
  };

  const themeButtonStyle = {
    padding: '0.5rem',
    borderRadius: '0.5rem',
    backgroundColor: isDark ? '#374151' : '#f3f4f6',
    border: 'none',
    cursor: 'pointer',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    transition: 'background-color 0.2s'
  };

  return (
    <header style={headerStyle}>
      <div style={containerStyle}>
        <div style={flexStyle}>
          {/* Logo and Title */}
          <div style={logoContainerStyle}>
            <div style={logoStyle}>
              <Download size={24} color="white" />
            </div>
            <div>
              <h1 style={titleStyle}>
                Image Downloader
              </h1>
              <p style={subtitleStyle}>
                Download images from any URL
              </p>
            </div>
          </div>

          {/* Theme Toggle */}
          <button
            onClick={toggleTheme}
            style={themeButtonStyle}
            aria-label="Toggle theme"
          >
            {isDark ? (
              <Sun size={20} color="#fbbf24" />
            ) : (
              <Moon size={20} color="#4b5563" />
            )}
          </button>
        </div>
      </div>
    </header>
  );
};

export default Header;
