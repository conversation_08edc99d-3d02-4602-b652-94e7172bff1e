// Image service for fetching images from URLs
export class ImageService {
  static async fetchImagesFromUrl(url) {
    try {
      console.log('Fetching images from:', url);

      let extractedImages = [];

      // First try to fetch images using a CORS proxy
      try {
        extractedImages = await this.fetchImagesWithProxy(url);
        console.log(`Proxy fetch found ${extractedImages.length} images`);
      } catch (proxyError) {
        console.log('Proxy fetch failed, trying direct fetch...');
      }

      // If proxy didn't work or found no images, try direct fetch
      if (extractedImages.length === 0) {
        try {
          extractedImages = await this.fetchImagesDirect(url);
          console.log(`Direct fetch found ${extractedImages.length} images`);
        } catch (directError) {
          console.log('Direct fetch also failed');
        }
      }

      // If we found images, validate them
      if (extractedImages.length > 0) {
        const validImages = await this.validateAndFilterImages(extractedImages);
        if (validImages.length > 0) {
          return validImages;
        }
      }

      // If no real images found, return demo images with a message
      console.log('No valid images found, falling back to demo images');
      const demoImages = this.getDemoImages();

      // Add a notice that these are demo images
      return [{
        id: 'notice',
        url: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjNmNGY2Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxOCIgZmlsbD0iIzM3NDE1MSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPk5vIGltYWdlcyBmb3VuZCBhdCB0aGlzIFVSTDwvdGV4dD48dGV4dCB4PSI1MCUiIHk9IjY1JSIgZm9udC1mYW1pbHk9IkFyaWFsLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjE0IiBmaWxsPSIjNmI3MjgwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSI+U2hvd2luZyBkZW1vIGltYWdlcyBpbnN0ZWFkPC90ZXh0Pjwvc3ZnPg==',
        alt: 'No images found - showing demo images',
        width: 400,
        height: 300,
        selected: false
      }, ...demoImages];
    } catch (error) {
      console.error('Error fetching images:', error);
      return this.getDemoImages();
    }
  }

  static async fetchImagesWithProxy(url) {
    // Try multiple CORS proxy services
    const proxies = [
      {
        name: 'allorigins.win',
        url: `https://api.allorigins.win/get?url=${encodeURIComponent(url)}`,
        parseResponse: (data) => data.contents
      },
      {
        name: 'cors-anywhere (demo)',
        url: `https://cors-anywhere.herokuapp.com/${url}`,
        parseResponse: (data) => data
      },
      {
        name: 'thingproxy',
        url: `https://thingproxy.freeboard.io/fetch/${url}`,
        parseResponse: (data) => data
      }
    ];

    for (const proxy of proxies) {
      try {
        console.log(`Trying proxy: ${proxy.name}`);
        const response = await fetch(proxy.url);

        if (!response.ok) {
          console.log(`${proxy.name} failed with status: ${response.status}`);
          continue;
        }

        let data;
        const contentType = response.headers.get('content-type');

        if (contentType && contentType.includes('application/json')) {
          data = await response.json();
          data = proxy.parseResponse(data);
        } else {
          data = await response.text();
          data = proxy.parseResponse(data);
        }

        if (!data) {
          console.log(`${proxy.name} returned no content`);
          continue;
        }

        console.log(`${proxy.name} succeeded`);
        return this.extractImagesFromHtml(data, url);
      } catch (error) {
        console.error(`${proxy.name} failed:`, error);
        continue;
      }
    }

    console.log('All proxy services failed');
    return [];
  }

  static async fetchImagesDirect(url) {
    try {
      // Try direct fetch (will only work if the site allows CORS)
      const response = await fetch(url, {
        mode: 'cors',
        headers: {
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const html = await response.text();
      return this.extractImagesFromHtml(html, url);
    } catch (error) {
      console.error('Direct fetch failed:', error);
      return [];
    }
  }
  
  static extractImagesFromHtml(html, baseUrl) {
    try {
      const parser = new DOMParser();
      const doc = parser.parseFromString(html, 'text/html');

      // Get all img elements
      const imgElements = doc.querySelectorAll('img');

      // Also look for images in other places
      const bgImages = this.extractBackgroundImages(doc, baseUrl);
      const pictureImages = this.extractPictureImages(doc, baseUrl);

      const images = [];
      const seenUrls = new Set(); // Prevent duplicates

      // Process img elements
      imgElements.forEach((img, index) => {
        const imageData = this.processImageElement(img, index, baseUrl);
        if (imageData && !seenUrls.has(imageData.url)) {
          seenUrls.add(imageData.url);
          images.push(imageData);
        }
      });

      // Add background images
      bgImages.forEach(imageData => {
        if (!seenUrls.has(imageData.url)) {
          seenUrls.add(imageData.url);
          images.push(imageData);
        }
      });

      // Add picture images
      pictureImages.forEach(imageData => {
        if (!seenUrls.has(imageData.url)) {
          seenUrls.add(imageData.url);
          images.push(imageData);
        }
      });

      console.log(`Extracted ${images.length} images from ${baseUrl}`);
      return images;
    } catch (error) {
      console.error('Error parsing HTML:', error);
      return [];
    }
  }

  static processImageElement(img, index, baseUrl) {
    // Try multiple attributes for image source
    let src = img.src ||
              img.getAttribute('data-src') ||
              img.getAttribute('data-lazy-src') ||
              img.getAttribute('data-original') ||
              img.getAttribute('data-srcset') ||
              img.getAttribute('srcset');

    if (!src) return null;

    // Handle srcset - take the first URL
    if (src.includes(' ')) {
      src = src.split(' ')[0];
    }

    // Convert relative URLs to absolute
    src = this.makeAbsoluteUrl(src, baseUrl);

    if (!src || !this.isValidImageUrl(src)) {
      return null;
    }

    // Filter out very small images and common non-content images
    const width = parseInt(img.getAttribute('width')) || 0;
    const height = parseInt(img.getAttribute('height')) || 0;

    // Skip very small images (likely icons)
    if ((width > 0 && width < 50) || (height > 0 && height < 50)) {
      return null;
    }

    // Skip common tracking/icon images
    if (this.isTrackingOrIconImage(src)) {
      return null;
    }

    return {
      id: `img-${Date.now()}-${index}`,
      url: src,
      alt: img.alt || img.title || `Image ${index + 1}`,
      width: width || null,
      height: height || null,
      selected: false
    };
  }

  static extractBackgroundImages(doc, baseUrl) {
    const images = [];
    const elements = doc.querySelectorAll('*');

    elements.forEach((element, index) => {
      const style = element.getAttribute('style');
      if (style && style.includes('background-image')) {
        const match = style.match(/background-image:\s*url\(['"]?([^'"]+)['"]?\)/);
        if (match) {
          const src = this.makeAbsoluteUrl(match[1], baseUrl);
          if (src && this.isValidImageUrl(src) && !this.isTrackingOrIconImage(src)) {
            images.push({
              id: `bg-${Date.now()}-${index}`,
              url: src,
              alt: `Background Image ${index + 1}`,
              width: null,
              height: null,
              selected: false
            });
          }
        }
      }
    });

    return images;
  }

  static extractPictureImages(doc, baseUrl) {
    const images = [];
    const pictureElements = doc.querySelectorAll('picture source, picture img');

    pictureElements.forEach((element, index) => {
      const src = element.getAttribute('srcset') || element.getAttribute('src');
      if (src) {
        let imageUrl = src;
        // Handle srcset - take the first URL
        if (imageUrl.includes(' ')) {
          imageUrl = imageUrl.split(' ')[0];
        }

        imageUrl = this.makeAbsoluteUrl(imageUrl, baseUrl);
        if (imageUrl && this.isValidImageUrl(imageUrl) && !this.isTrackingOrIconImage(imageUrl)) {
          images.push({
            id: `picture-${Date.now()}-${index}`,
            url: imageUrl,
            alt: element.alt || `Picture Image ${index + 1}`,
            width: null,
            height: null,
            selected: false
          });
        }
      }
    });

    return images;
  }

  static makeAbsoluteUrl(src, baseUrl) {
    if (!src) return null;

    try {
      // Already absolute
      if (src.startsWith('http://') || src.startsWith('https://')) {
        return src;
      }

      // Protocol relative
      if (src.startsWith('//')) {
        return 'https:' + src;
      }

      // Absolute path
      if (src.startsWith('/')) {
        const urlObj = new URL(baseUrl);
        return urlObj.origin + src;
      }

      // Relative path
      return new URL(src, baseUrl).href;
    } catch (error) {
      console.error('Error making absolute URL:', error);
      return null;
    }
  }

  static isValidImageUrl(url) {
    if (!url) return false;

    // Check for common image extensions
    const imageExtensions = /\.(jpg|jpeg|png|gif|webp|svg|bmp|ico)(\?.*)?$/i;
    if (imageExtensions.test(url)) {
      return true;
    }

    // Check for data URLs
    if (url.startsWith('data:image/')) {
      return true;
    }

    // Check for common image hosting domains
    const imageHosts = [
      'imgur.com', 'flickr.com', 'unsplash.com', 'pexels.com',
      'pixabay.com', 'shutterstock.com', 'getty', 'cloudinary.com',
      'amazonaws.com', 'googleusercontent.com', 'cdninstagram.com'
    ];

    return imageHosts.some(host => url.includes(host));
  }

  static isTrackingOrIconImage(url) {
    const trackingPatterns = [
      'tracking', 'pixel', 'analytics', 'favicon', 'logo',
      'icon', 'sprite', 'button', 'badge', 'avatar',
      '1x1', 'transparent', 'spacer', 'blank'
    ];

    const lowerUrl = url.toLowerCase();
    return trackingPatterns.some(pattern => lowerUrl.includes(pattern));
  }
  
  static getDemoImages() {
    // Demo images for testing
    return [
      {
        id: 'demo-1',
        url: 'https://picsum.photos/400/300?random=1',
        alt: 'Random Demo Image 1',
        width: 400,
        height: 300,
        selected: false
      },
      {
        id: 'demo-2',
        url: 'https://picsum.photos/400/300?random=2',
        alt: 'Random Demo Image 2',
        width: 400,
        height: 300,
        selected: false
      },
      {
        id: 'demo-3',
        url: 'https://picsum.photos/400/300?random=3',
        alt: 'Random Demo Image 3',
        width: 400,
        height: 300,
        selected: false
      },
      {
        id: 'demo-4',
        url: 'https://picsum.photos/400/300?random=4',
        alt: 'Random Demo Image 4',
        width: 400,
        height: 300,
        selected: false
      },
      {
        id: 'demo-5',
        url: 'https://picsum.photos/400/300?random=5',
        alt: 'Random Demo Image 5',
        width: 400,
        height: 300,
        selected: false
      },
      {
        id: 'demo-6',
        url: 'https://picsum.photos/400/300?random=6',
        alt: 'Random Demo Image 6',
        width: 400,
        height: 300,
        selected: false
      }
    ];
  }

  static getUnsplashDemoImages() {
    return [
      {
        id: 'unsplash-1',
        url: 'https://picsum.photos/400/300?random=10',
        alt: 'Professional Photo 1',
        width: 400,
        height: 300,
        selected: false
      },
      {
        id: 'unsplash-2',
        url: 'https://picsum.photos/400/300?random=11',
        alt: 'Professional Photo 2',
        width: 400,
        height: 300,
        selected: false
      },
      {
        id: 'unsplash-3',
        url: 'https://picsum.photos/400/300?random=12',
        alt: 'Professional Photo 3',
        width: 400,
        height: 300,
        selected: false
      },
      {
        id: 'unsplash-4',
        url: 'https://picsum.photos/400/300?random=13',
        alt: 'Professional Photo 4',
        width: 400,
        height: 300,
        selected: false
      }
    ];
  }

  static getNatureDemoImages() {
    return [
      {
        id: 'nature-1',
        url: 'https://picsum.photos/400/300?random=20',
        alt: 'Nature Landscape 1',
        width: 400,
        height: 300,
        selected: false
      },
      {
        id: 'nature-2',
        url: 'https://picsum.photos/400/300?random=21',
        alt: 'Nature Landscape 2',
        width: 400,
        height: 300,
        selected: false
      },
      {
        id: 'nature-3',
        url: 'https://picsum.photos/400/300?random=22',
        alt: 'Nature Landscape 3',
        width: 400,
        height: 300,
        selected: false
      },
      {
        id: 'nature-4',
        url: 'https://picsum.photos/400/300?random=23',
        alt: 'Nature Landscape 4',
        width: 400,
        height: 300,
        selected: false
      },
      {
        id: 'nature-5',
        url: 'https://picsum.photos/400/300?random=24',
        alt: 'Nature Landscape 5',
        width: 400,
        height: 300,
        selected: false
      }
    ];
  }
  
  static async validateAndFilterImages(images) {
    console.log(`Validating ${images.length} images...`);
    const validImages = [];

    // Process images in batches to avoid overwhelming the browser
    const batchSize = 10;
    for (let i = 0; i < images.length; i += batchSize) {
      const batch = images.slice(i, i + batchSize);
      const batchPromises = batch.map(image => this.validateImage(image));
      const batchResults = await Promise.allSettled(batchPromises);

      batchResults.forEach((result, index) => {
        if (result.status === 'fulfilled' && result.value) {
          validImages.push(result.value);
        }
      });
    }

    console.log(`${validImages.length} valid images found`);
    return validImages;
  }

  static async validateImage(image) {
    return new Promise((resolve) => {
      const img = new Image();
      const timeout = setTimeout(() => {
        resolve(null); // Timeout - image failed to load
      }, 5000); // 5 second timeout

      img.onload = () => {
        clearTimeout(timeout);
        // Update dimensions if we got them
        const updatedImage = {
          ...image,
          width: img.naturalWidth,
          height: img.naturalHeight
        };
        resolve(updatedImage);
      };

      img.onerror = () => {
        clearTimeout(timeout);
        resolve(null); // Image failed to load
      };

      // Handle CORS issues
      img.crossOrigin = 'anonymous';
      img.src = image.url;
    });
  }

  static async downloadImage(imageUrl, filename) {
    try {
      // Try to fetch with CORS first
      let response;
      try {
        response = await fetch(imageUrl, { mode: 'cors' });
      } catch (corsError) {
        // If CORS fails, try with no-cors mode
        response = await fetch(imageUrl, { mode: 'no-cors' });
      }

      if (!response.ok && response.type !== 'opaque') {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const blob = await response.blob();

      // Create download link
      const link = document.createElement('a');
      link.href = URL.createObjectURL(blob);
      link.download = filename || this.getFilenameFromUrl(imageUrl);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(link.href);
    } catch (error) {
      console.error('Error downloading image:', error);
      // Fallback: open image in new tab
      window.open(imageUrl, '_blank');
    }
  }

  static getFilenameFromUrl(url) {
    try {
      const urlObj = new URL(url);
      const pathname = urlObj.pathname;
      const filename = pathname.split('/').pop();

      if (filename && filename.includes('.')) {
        return filename;
      }

      // Generate filename with extension based on URL
      const extension = this.getImageExtensionFromUrl(url) || 'jpg';
      return `image-${Date.now()}.${extension}`;
    } catch (error) {
      return `image-${Date.now()}.jpg`;
    }
  }

  static getImageExtensionFromUrl(url) {
    const match = url.match(/\.(jpg|jpeg|png|gif|webp|svg|bmp)(\?.*)?$/i);
    return match ? match[1].toLowerCase() : null;
  }
}
