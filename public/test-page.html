<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Page with Images</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .gallery {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .gallery img {
            width: 100%;
            height: 200px;
            object-fit: cover;
            border-radius: 8px;
        }
        .hero {
            background-image: url('https://picsum.photos/800/300?random=100');
            height: 300px;
            background-size: cover;
            background-position: center;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }
        .lazy-image {
            width: 100%;
            height: 150px;
            object-fit: cover;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <h1>Test Page for Image Downloader</h1>
    <p>This page contains various types of images to test the image extraction functionality.</p>
    
    <!-- Hero section with background image -->
    <div class="hero">
        <h2>Hero Section with Background Image</h2>
    </div>
    
    <!-- Regular img tags -->
    <h2>Regular Images</h2>
    <div class="gallery">
        <img src="https://picsum.photos/400/300?random=1" alt="Random Image 1">
        <img src="https://picsum.photos/400/300?random=2" alt="Random Image 2">
        <img src="https://picsum.photos/400/300?random=3" alt="Random Image 3">
        <img src="https://picsum.photos/400/300?random=4" alt="Random Image 4">
    </div>
    
    <!-- Lazy loaded images -->
    <h2>Lazy Loaded Images</h2>
    <div class="gallery">
        <img class="lazy-image" data-src="https://picsum.photos/400/300?random=5" alt="Lazy Image 1">
        <img class="lazy-image" data-src="https://picsum.photos/400/300?random=6" alt="Lazy Image 2">
        <img class="lazy-image" data-original="https://picsum.photos/400/300?random=7" alt="Lazy Image 3">
    </div>
    
    <!-- Picture element -->
    <h2>Responsive Picture Element</h2>
    <picture>
        <source media="(min-width: 800px)" srcset="https://picsum.photos/800/400?random=8">
        <source media="(min-width: 400px)" srcset="https://picsum.photos/400/300?random=9">
        <img src="https://picsum.photos/300/200?random=10" alt="Responsive Image">
    </picture>
    
    <!-- Images with srcset -->
    <h2>Images with Srcset</h2>
    <img srcset="https://picsum.photos/400/300?random=11 400w, https://picsum.photos/800/600?random=12 800w" 
         sizes="(max-width: 600px) 400px, 800px"
         src="https://picsum.photos/400/300?random=11" 
         alt="Srcset Image">
    
    <!-- Some small images that should be filtered out -->
    <h2>Small Images (should be filtered)</h2>
    <img src="https://picsum.photos/20/20?random=13" alt="Small Icon" width="20" height="20">
    <img src="https://picsum.photos/30/30?random=14" alt="Another Small Icon" width="30" height="30">
    
    <p>This test page contains:</p>
    <ul>
        <li>Background images in CSS</li>
        <li>Regular img tags</li>
        <li>Lazy-loaded images with data-src attributes</li>
        <li>Picture elements with multiple sources</li>
        <li>Images with srcset for responsive design</li>
        <li>Small images that should be filtered out</li>
    </ul>
    
    <script>
        // Simple lazy loading simulation
        document.addEventListener('DOMContentLoaded', function() {
            const lazyImages = document.querySelectorAll('.lazy-image');
            lazyImages.forEach(img => {
                const src = img.getAttribute('data-src') || img.getAttribute('data-original');
                if (src) {
                    img.src = src;
                }
            });
        });
    </script>
</body>
</html>
