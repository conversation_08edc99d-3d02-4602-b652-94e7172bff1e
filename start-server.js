#!/usr/bin/env node

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔍 Searching for image-downloader project...');

// Check if we're already in the project directory
if (fs.existsSync('package.json')) {
  try {
    const pkg = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    if (pkg.name === 'image-downloader') {
      console.log('✅ Found project in current directory');
      console.log('🚀 Starting development server...');
      
      const npmProcess = spawn('npm', ['run', 'dev'], {
        stdio: 'inherit',
        shell: true
      });
      
      npmProcess.on('error', (error) => {
        console.error('❌ Error starting server:', error.message);
      });
      
      return;
    }
  } catch (e) {
    console.log('❌ Error reading package.json');
  }
}

console.log('❌ Could not find image-downloader project');
console.log('📋 Manual setup required:');
console.log('1. Navigate to your project directory');
console.log('2. Run: npm install');
console.log('3. Run: npm run dev');
console.log('4. Open: http://localhost:5173/');
